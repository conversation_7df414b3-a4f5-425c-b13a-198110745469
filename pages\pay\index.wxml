<view class="customer-box data-v-32f2f1fc"><view class="wrap data-v-32f2f1fc"><view class="contion data-v-32f2f1fc"><view class="orderPay data-v-32f2f1fc"><view class="data-v-32f2f1fc"><block wx:if="{{timeout}}"><view class="data-v-32f2f1fc">订单已超时</view></block><block wx:else><view class="data-v-32f2f1fc">支付剩余时间<text class="data-v-32f2f1fc">{{rocallTime}}</text></view></block></view><view class="money data-v-32f2f1fc">￥<text class="data-v-32f2f1fc">{{orderDataInfo.orderAmount}}</text></view><view class="data-v-32f2f1fc">{{"苍穹餐厅-"+orderDataInfo.orderNumber}}</view></view></view><view class="box payBox data-v-32f2f1fc"><view class="contion data-v-32f2f1fc"><view class="example-body data-v-32f2f1fc"><radio-group data-event-opts="{{[['change',[['styleChange',['$event']]]]]}}" class="uni-list data-v-32f2f1fc" bindchange="__e"><view class="uni-list-item data-v-32f2f1fc"><block wx:for="{{payMethodList}}" wx:for-item="item" wx:for-index="index" wx:key="*this"><view class="uni-list-item__container data-v-32f2f1fc"><view class="uni-list-item__content data-v-32f2f1fc"><icon class="wechatIcon data-v-32f2f1fc"></icon><text class="uni-list-item__content-title data-v-32f2f1fc">{{item}}</text></view><view class="uni-list-item__extra data-v-32f2f1fc"><radio class="radioIcon data-v-32f2f1fc" value="{{item}}" color="#FFC200" checked="{{index==activeRadio}}"></radio></view></view></block></view></radio-group></view></view></view><view class="bottomBox btnBox data-v-32f2f1fc"><button class="add_btn data-v-32f2f1fc" type="primary" plain="true" data-event-opts="{{[['tap',[['handleSave',['$event']]]]]}}" bindtap="__e">确认支付</button></view></view></view>