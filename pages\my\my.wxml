<view class="data-v-0be17cc6"><uni-nav-bar vue-id="704e9d00-1" left-icon="back" leftIcon="arrowleft" title="地址管理" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#ffc200" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-0be17cc6" bind:__l="__l"></uni-nav-bar><view class="my-center data-v-0be17cc6"><view class="my_info data-v-0be17cc6"><view class="head data-v-0be17cc6"><image class="head_image data-v-0be17cc6" src="{{psersonUrl}}"></image></view><view class="phone_name data-v-0be17cc6"><view class="name data-v-0be17cc6"><text class="name_text data-v-0be17cc6">{{nickName}}</text><block wx:if="{{gender===2}}"><image class="name_type data-v-0be17cc6" src="../../static/girl.png"></image></block><block wx:if="{{gender===1}}"><image class="name_type data-v-0be17cc6" src="../../static/boy.png"></image></block></view><view class="phone data-v-0be17cc6"><text class="phone_text data-v-0be17cc6">{{$root.f0}}</text></view></view></view><view class="container data-v-0be17cc6"><view class="box address_order data-v-0be17cc6"><view data-event-opts="{{[['tap',[['goAddress',['$event']]]]]}}" class="address data-v-0be17cc6" bindtap="__e"><image class="location data-v-0be17cc6" src="../../static/address.png"></image><text class="address_word data-v-0be17cc6">地址管理</text><image class="to_right data-v-0be17cc6" src="../../static/toRight.png" mode></image></view><view data-event-opts="{{[['tap',[['goOrder',['$event']]]]]}}" class="order data-v-0be17cc6" bindtap="__e"><image class="location data-v-0be17cc6" src="../../static/order.png"></image><text class="order_word data-v-0be17cc6">历史订单</text><image class="to_right data-v-0be17cc6" src="../../static/toRight.png" mode></image></view></view><block wx:if="{{recentOrdersList&&recentOrdersList.length>0}}"><view class="recent data-v-0be17cc6"><text class="order_line data-v-0be17cc6">最近订单</text></view></block><scroll-view style="{{'height:'+(scrollH+'px')+';'}}" scroll-y="true" data-event-opts="{{[['scrolltolower',[['lower',['$event']]]]]}}" bindscrolltolower="__e" class="data-v-0be17cc6"><view class="main recent_orders data-v-0be17cc6"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="box order_lists data-v-0be17cc6"><view class="date_type data-v-0be17cc6"><text class="time data-v-0be17cc6">{{item.$orig.orderTime}}</text><text class="{{['type','status','data-v-0be17cc6',(item.$orig.status==2)?'status':'']}}">{{item.m0}}</text></view><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" class="orderBox data-v-0be17cc6" bindtap="__e"><view class="food_num data-v-0be17cc6"><scroll-view class="pic data-v-0be17cc6" style="width:100%;overflow:hidden;white-space:nowrap;" scroll-x="true"><block wx:for="{{item.$orig.orderDetailList}}" wx:for-item="num" wx:for-index="y" wx:key="y"><view class="food_num_item data-v-0be17cc6"><view class="img data-v-0be17cc6"><image src="{{num.image}}" class="data-v-0be17cc6"></image></view><view class="food data-v-0be17cc6">{{num.name}}</view></view></block></scroll-view></view><view class="numAndAum data-v-0be17cc6"><view class="data-v-0be17cc6"><text class="data-v-0be17cc6">{{"￥"+item.g0}}</text></view><view class="data-v-0be17cc6"><text class="data-v-0be17cc6">{{"共"+item.m1.count+"件"}}</text></view></view></view><view class="againBtn data-v-0be17cc6"><button class="new_btn data-v-0be17cc6" type="default" data-event-opts="{{[['tap',[['oneOrderFun',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" bindtap="__e">再来一单</button><block wx:if="{{item.m2}}"><button class="new_btn btn data-v-0be17cc6" type="default" data-event-opts="{{[['tap',[['goDetail',['$0'],[[['recentOrdersList','',index,'id']]]]]]]}}" bindtap="__e">去支付</button></block></view></view></block></view><block wx:if="{{loading}}"><reach-bottom vue-id="704e9d00-2" loadingText="{{loadingText}}" class="data-v-0be17cc6" bind:__l="__l"></reach-bottom></block></scroll-view></view></view></view>