<view class="data-v-0ca91b30"><uni-nav-bar vue-id="3bc35b9e-1" left-icon="back" leftIcon="arrowleft" title="提交订单" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-0ca91b30" bind:__l="__l"></uni-nav-bar><view data-event-opts="{{[['touchstart',[['touchstart',['$event']]]]]}}" class="order_content data-v-0ca91b30" bindtouchstart="__e"><view class="order_content_box data-v-0ca91b30"><view class="new_address data-v-0ca91b30"><view data-event-opts="{{[['tap',[['goAddress',['$event']]]]]}}" class="top data-v-0ca91b30" bindtap="__e"><block wx:if="{{!address}}"><view class="address_name_disabled data-v-0ca91b30">请选择收货地址</view></block><block wx:else><view class="address_name data-v-0ca91b30"><view class="address data-v-0ca91b30"><text class="{{['tag','data-v-0ca91b30','tag'+tagLabel]}}">{{addressLabel}}</text><text class="word data-v-0ca91b30">{{address}}</text></view><view class="name data-v-0ca91b30"><text class="name_1 data-v-0ca91b30">{{nickName}}</text><text class="name_2 data-v-0ca91b30">{{phoneNumber}}</text></view><block wx:if="{{address}}"><view class="infoTip data-v-0ca91b30">为减少接触，降低风险，推荐使用无接触配送</view></block></view></block><view class="address_image data-v-0ca91b30"><view class="to_right data-v-0ca91b30"></view></view></view><view class="bottom data-v-0ca91b30"><view data-event-opts="{{[['tap',[['openTimePopuo',['bottom']]]]]}}" class="bottomTime _div data-v-0ca91b30" bindtap="__e"><text class="time_name_disabled data-v-0ca91b30">立即送出</text><view class="address_image data-v-0ca91b30"><text class="data-v-0ca91b30">{{arrivalTime+"送达"}}</text><view class="to_right data-v-0ca91b30"></view></view></view><block wx:if="{{address}}"><view class="infoTip data-v-0ca91b30">因配送订单较多，送达时间可能波动</view></block></view></view><view class="order_list_cont data-v-0ca91b30"><view class="box order_list data-v-0ca91b30"><view class="word_text data-v-0ca91b30"><text class="word_style data-v-0ca91b30">苍穹餐厅</text></view><view class="order-type data-v-0ca91b30"><block wx:for="{{$root.l0}}" wx:for-item="obj" wx:for-index="index" wx:key="index"><view class="type_item data-v-0ca91b30"><view class="dish_img data-v-0ca91b30"><image class="dish_img_url data-v-0ca91b30" mode="aspectFill" src="{{obj.$orig.image}}"></image></view><view class="dish_info data-v-0ca91b30"><view class="dish_name data-v-0ca91b30">{{''+obj.$orig.name+''}}</view><block wx:if="{{obj.$orig.dishFlavor}}"><view class="dish_dishFlavor data-v-0ca91b30">{{''+obj.$orig.dishFlavor+''}}</view></block><view class="dish_price data-v-0ca91b30">×<block wx:if="{{obj.$orig.number&&obj.$orig.number>0}}"><text class="dish_number data-v-0ca91b30">{{obj.$orig.number}}</text></block></view><view class="dish_active data-v-0ca91b30"><text class="data-v-0ca91b30">￥</text>{{''+obj.g0+''}}</view></view></view></block><view class="iconUp data-v-0ca91b30"><block wx:if="{{orderListDataes.length>3}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="data-v-0ca91b30"><text class="data-v-0ca91b30">{{!showDisplay?'展开更多':'点击收起'}}</text><image class="{{['icon_img','data-v-0ca91b30',showDisplay?'icon_imgDown':'']}}" src="../../static/toRight.png" mode></image></view></block></view><view class="orderList data-v-0ca91b30"><view class="orderInfo data-v-0ca91b30"><text class="text data-v-0ca91b30">打包费</text><text class="may data-v-0ca91b30">￥</text>{{orderDishNumber+''}}</view><view class="orderInfo data-v-0ca91b30"><text class="text data-v-0ca91b30">配送费</text><text class="may data-v-0ca91b30">￥</text>6</view><view class="totalMoney data-v-0ca91b30">合计<text class="text data-v-0ca91b30"><text class="data-v-0ca91b30">￥</text>{{$root.g1}}</text></view></view></view></view><view class="boxPad data-v-0ca91b30"><view class="box order_list data-v-0ca91b30"><view class="uniInfo data-v-0ca91b30"><view data-event-opts="{{[['tap',[['goRemark',['$event']]]]]}}" bindtap="__e" class="data-v-0ca91b30"><uni-list vue-id="3bc35b9e-2" class="data-v-0ca91b30" bind:__l="__l" vue-slots="{{['default']}}"><uni-list-item class="uniListItem data-v-0ca91b30" vue-id="{{('3bc35b9e-3')+','+('3bc35b9e-2')}}" showArrow="{{true}}" title="备注" bind:__l="__l" vue-slots="{{['footer']}}"><text class="temarkText data-v-0ca91b30" slot="footer">{{remark?remark:'推荐使用无接触配送'}}</text></uni-list-item></uni-list></view><view data-event-opts="{{[['tap',[['openPopuos',['bottom']]]]]}}" bindtap="__e" class="data-v-0ca91b30"><uni-list vue-id="3bc35b9e-4" class="data-v-0ca91b30" bind:__l="__l" vue-slots="{{['default']}}"><uni-list-item class="uniListItem data-v-0ca91b30" vue-id="{{('3bc35b9e-5')+','+('3bc35b9e-4')}}" showArrow="{{true}}" title="餐具数量" bind:__l="__l" vue-slots="{{['footer']}}"><text class="data-v-0ca91b30" slot="footer">{{"已在店选择："+tablewareData}}</text></uni-list-item></uni-list></view><view class="invoiceBox data-v-0ca91b30"><uni-list vue-id="3bc35b9e-6" class="data-v-0ca91b30" bind:__l="__l" vue-slots="{{['default']}}"><uni-list-item class="uniListItem data-v-0ca91b30" vue-id="{{('3bc35b9e-7')+','+('3bc35b9e-6')}}" title="发票" bind:__l="__l" vue-slots="{{['footer']}}"><text class="data-v-0ca91b30" slot="footer">请联系商家提供</text></uni-list-item></uni-list></view><view class="container data-v-0ca91b30"><uni-popup class="popupBox data-v-0ca91b30 vue-ref" bind:change="__e" vue-id="3bc35b9e-8" data-ref="popup" data-event-opts="{{[['^change',[['change']]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-0ca91b30"><view class="popupTitle data-v-0ca91b30"><text class="data-v-0ca91b30">按政府条例要求：</text><text class="data-v-0ca91b30">商家不得主动向您提供一次性餐具，请按需选择餐具数量</text></view><view class="popupCon data-v-0ca91b30"><view class="popupBtn data-v-0ca91b30"><text data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" bindtap="__e" class="data-v-0ca91b30">取消</text><text class="data-v-0ca91b30">选择本单餐具</text><text data-event-opts="{{[['tap',[['handlePiker',['$event']]]]]}}" bindtap="__e" class="data-v-0ca91b30">确定</text></view><pikers vue-id="{{('3bc35b9e-9')+','+('3bc35b9e-8')}}" baseData="{{baseData}}" data-ref="piker" data-event-opts="{{[['^changeCont',[['changeCont']]]]}}" bind:changeCont="__e" class="data-v-0ca91b30 vue-ref" bind:__l="__l"></pikers></view><view class="popupSet data-v-0ca91b30"><view class="data-v-0ca91b30">后续订单餐具设置</view><view class="data-v-0ca91b30"><radio-group data-event-opts="{{[['change',[['handleRadio',['$event']]]]]}}" bindchange="__e" class="data-v-0ca91b30"><block wx:for="{{radioGroup}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><label class="data-v-0ca91b30"><radio value="{{item}}" color="#FFC200" checked="{{item==activeRadio}}" class="data-v-0ca91b30"></radio>{{item+''}}</label></block></radio-group></view></view></view></uni-popup><uni-popup class="popupBox data-v-0ca91b30 vue-ref" bind:change="__e" vue-id="3bc35b9e-10" data-ref="timePopup" data-event-opts="{{[['^change',[['change']]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-0ca91b30"><view class="pickerCon data-v-0ca91b30"><view class="dayBox data-v-0ca91b30"><scroll-view scroll-x="true" scroll-into-view="{{scrollinto}}" scroll-with-animation="{{true}}" class="data-v-0ca91b30"><block wx:for="{{popleft}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="scroll-row-item data-v-0ca91b30" id="{{'tab'+index}}" data-event-opts="{{[['tap',[['dateChange',[index]]]]]}}" bindtap="__e"><block wx:for="{{weeks}}" wx:for-item="val" wx:for-index="i" wx:key="i"><view class="data-v-0ca91b30"><block wx:if="{{index===i}}"><view class="{{['data-v-0ca91b30',tabIndex==index?'scroll-row-day':'']}}"><text class="line data-v-0ca91b30"></text>{{item}}<text class="week data-v-0ca91b30">{{"("+val+")"}}</text></view></block></view></block></view></block></scroll-view></view><view class="timeBox data-v-0ca91b30"><scroll-view class="card_order_list data-v-0ca91b30" scroll-y="true" scroll-top="40rpx"><block wx:for="{{newDateData}}" wx:for-item="val" wx:for-index="i" wx:key="i"><view data-event-opts="{{[['tap',[['timeClick',['$0',i],[[['newDateData','',i]]]]]]]}}" class="{{['item','data-v-0ca91b30',selectValue===i?'city-column_select':'']}}" bindtap="__e">{{val}}</view></block></scroll-view></view></view><view data-event-opts="{{[['tap',[['onsuer',['$event']]]]]}}" class="btns data-v-0ca91b30" bindtap="__e">取消</view></view></uni-popup></view></view></view></view></view></view><view class="footer_order_buttom order_form data-v-0ca91b30"><view class="order_number data-v-0ca91b30"><image class="order_number_icon data-v-0ca91b30" src="../../static/btn_waiter_sel.png" mode></image><view class="order_dish_num data-v-0ca91b30">{{''+orderDishNumber+''}}</view></view><view class="order_price data-v-0ca91b30"><text class="ico data-v-0ca91b30">￥</text>{{''+$root.g2+''}}</view><view class="order_but data-v-0ca91b30"><block wx:if="{{isHandlePy}}"><view class="order_but_rit data-v-0ca91b30">去支付</view></block><block wx:else><view data-event-opts="{{[['tap',[['payOrderHandle']]]]}}" class="order_but_rit data-v-0ca91b30" bindtap="__e">去支付</view></block></view></view></view></view>