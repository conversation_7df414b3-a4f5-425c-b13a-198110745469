<view class="customer-box data-v-27c0a608"><uni-nav-bar vue-id="d05318ec-1" left-icon="back" leftIcon="arrowleft" title="订单备注" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-27c0a608" bind:__l="__l"></uni-nav-bar><view class="wrap data-v-27c0a608"><view class="box  data-v-27c0a608"><view class="contion data-v-27c0a608"><view class="order_list data-v-27c0a608"><view class="uni-textarea data-v-27c0a608"><textarea class="{{['beizhu_text','data-v-27c0a608',(platform==='ios')?'beizhu_text_ios':'']}}" placeholder-class="textarea-placeholder" placeholder="无接触配送，将商品挂家门口或放前台，地址封闭管理时请电话联系" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]],['monitorInput',['$event']]]]]}}" value="{{remark}}" bindinput="__e">{{getVal}}</textarea><text class="numText data-v-27c0a608"><text class="{{['data-v-27c0a608',numVal===0?'tip':'']}}">{{numVal}}</text>/50</text></view></view></view></view><view class="btnBox data-v-27c0a608"><button class="add_btn data-v-27c0a608" type="primary" plain="true" data-event-opts="{{[['tap',[['handleSaveRemark',['$event']]]]]}}" bindtap="__e">完成</button></view></view></view>