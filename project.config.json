{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "minified": true, "coverView": true, "es6": false, "postcss": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "uglifyFileName": false, "uploadWithSourceMap": true, "enhance": true, "useMultiFrameRuntime": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "ignoreUploadUnusedFiles": true, "checkInvalidKey": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useStaticServer": true, "showES6CompileOption": false, "useCompilerPlugins": false, "minifyWXML": true}, "compileType": "miniprogram", "libVersion": "2.24.2", "appid": "wxfce92bbc4a90d8cf", "projectname": "sky-take-out", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}