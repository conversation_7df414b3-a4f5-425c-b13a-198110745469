@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.box {
  display: flex;
  flex-direction: row;
}
.button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 35px;
  margin: 0 5px;
  border-radius: 5px;
}
.example-body {
  background-color: #fff;
  padding: 10px 0;
}
.button-text {
  color: #fff;
  font-size: 12px;
}
.popup-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 15px;
  height: 50px;
  background-color: #fff;
}
.popup-height {
  height: 100%;
  width: 200px;
}
.text {
  font-size: 12px;
  color: #333;
}
.popup-success {
  color: #fff;
  background-color: #e1f3d8;
}
.popup-warn {
  color: #fff;
  background-color: #faecd8;
}
.popup-error {
  color: #fff;
  background-color: #fde2e2;
}
.popup-info {
  color: #fff;
  background-color: #f2f6fc;
}
.success-text {
  color: #09bb07;
}
.warn-text {
  color: #e6a23c;
}
.error-text {
  color: #f56c6c;
}
.info-text {
  color: #909399;
}
.dialog,
.share {
  display: flex;
  flex-direction: column;
}
.dialog-box {
  padding: 10px;
}
.dialog .button,
.share .button {
  width: 100%;
  margin: 0;
  margin-top: 10px;
  padding: 3px 0;
  flex: 1;
}
.dialog-text {
  font-size: 14px;
  color: #333;
}

