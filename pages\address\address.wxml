<view class="customer-box data-v-db675620"><uni-nav-bar vue-id="91826bd4-1" left-icon="back" leftIcon="arrowleft" title="地址管理" statusBar="true" fixed="true" color="#ffffff" backgroundColor="#333333" data-event-opts="{{[['^clickLeft',[['goBack']]]]}}" bind:clickLeft="__e" class="data-v-db675620" bind:__l="__l"></uni-nav-bar><view class="address data-v-db675620" style="{{'height:'+('calc(100% - 136rpx - '+statusBarHeight+' - 44px - 20rpx)')+';'}}"><block wx:if="{{addressList&&addressList.length>0}}"><view class="address_content data-v-db675620"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="address_liests data-v-db675620"><view data-event-opts="{{[['tap',[['choseAddress',[index,'$0'],[[['addressList','',index]]]]]]]}}" class="list_item_top data-v-db675620" catchtap="__e"><view class="item_left data-v-db675620"><view class="details data-v-db675620"><text class="{{['tag','data-v-db675620','tag'+item.$orig.label]}}">{{item.m0}}</text><text class="address_word data-v-db675620">{{item.$orig.provinceName+item.$orig.cityName+item.$orig.districtName+item.$orig.detail}}</text></view><view class="sale data-v-db675620"><text class="name data-v-db675620">{{item.$orig.sex==='0'?item.$orig.consignee+' 男士':item.$orig.consignee+' 女士'}}</text><text class="num data-v-db675620">{{item.$orig.phone}}</text></view></view><view class="item_right data-v-db675620"><image class="edit data-v-db675620" src="../../static/edit.png" data-event-opts="{{[['tap',[['addOrEdit',['编辑','$0'],[[['addressList','',index]]]]]]]}}" catchtap="__e"></image></view></view><view class="list_item_bottom data-v-db675620"><label data-event-opts="{{[['tap',[['getRadio',[index,'$0'],[[['addressList','',index]]]]]]]}}" class="radio data-v-db675620" catchtap="__e"><block wx:if="{{testValue}}"><radio class="item_radio data-v-db675620" color="#FFC200" value="{{item.$orig.id}}" checked="{{item.$orig.isDefault===1&&isActive===index}}" data-event-opts="{{[['tap',[['getRadio',[index,'$0'],[[['addressList','',index]]]]]]]}}" catchtap="__e"></radio></block>设为默认地址</label></view></view></block></view></block><block wx:if="{{isEmpty}}"><empty vue-id="91826bd4-2" boxHeight="100%" textLabel="一个地址都没有哦" class="data-v-db675620" bind:__l="__l"></empty></block><view class="add_address data-v-db675620"><button class="add_btn data-v-db675620" type="primary" plain="true" data-event-opts="{{[['tap',[['addOrEdit',['新增']]]]]}}" bindtap="__e"><text class="add-icon data-v-db675620">+</text>新增收货地址</button></view></view></view>